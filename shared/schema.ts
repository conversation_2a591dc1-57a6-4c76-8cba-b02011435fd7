import { z } from "zod";

// TypeScript interfaces for the data models
export interface Scraper {
  id: string;
  itemName: string;
  url: string;
  selector: string;
  currentPrice: string | null;
  lowestPrice: string | null;
  previousPrice?: string | null;
  status: "active" | "updating" | "error";
  lastUpdated: Date;
  lastError: string | null;
  createdAt: Date;
}

export interface PriceHistory {
  id: string;
  scraperId: string;
  price: string;
  timestamp: Date;
}

// Zod schemas for validation
export const insertScraperSchema = z.object({
  itemName: z.string().min(1, "Item name is required"),
  url: z.string().url("Must be a valid URL"),
  selector: z.string().min(1, "CSS selector is required"),
});

export const updateScraperSchema = z.object({
  itemName: z.string().min(1, "Item name is required").optional(),
  url: z.string().url("Must be a valid URL").optional(),
  selector: z.string().min(1, "CSS selector is required").optional(),
  currentPrice: z.string().nullable().optional(),
  lowestPrice: z.string().nullable().optional(),
  status: z.enum(["active", "updating", "error"]).optional(),
  lastError: z.string().nullable().optional(),
});

// Type inference from schemas
export type InsertScraper = z.infer<typeof insertScraperSchema>;
export type UpdateScraper = z.infer<typeof updateScraperSchema>;
